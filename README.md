# 员工考勤系统

基于C语言、SQLite3、TCP网络编程和多线程技术开发的员工考勤管理系统。

## 项目特性

- **多线程服务器**: 支持多个客户端同时连接
- **角色权限管理**: 区分管理员和普通员工
- **考勤打卡**: 支持上班/下班打卡，自动计算工作时长和加班时长
- **迟到早退检测**: 自动检测迟到和早退，记录具体分钟数
- **数据统计**: 提供个人考勤记录查询、加班统计、迟到早退统计
- **管理功能**: 管理员可以添加/删除用户，查询所有用户考勤记录
- **日志记录**: 记录所有操作日志
- **跨平台**: 支持Linux系统

## 系统架构

```
├── server.c    # 服务器端（核心业务逻辑）
├── client.c    # 客户端（员工考勤界面）
├── admin.c     # 管理端（管理员操作界面）
├── company.db  # SQLite数据库文件
├── Makefile    # 编译脚本
└── README.md   # 说明文档
```

## 数据库设计

### 用户表 (users)
- id: 用户ID（主键）
- name: 用户名（唯一）
- password: 密码
- role: 角色（0=管理员，1=普通员工）

### 考勤记录表 (attendance)
- id: 记录ID（主键）
- user_id: 用户ID（外键）
- date: 日期
- check_in_time: 上班打卡时间
- check_out_time: 下班打卡时间
- work_hours: 工作时长
- overtime_hours: 加班时长
- is_late: 是否迟到（0=否，1=是）
- late_minutes: 迟到分钟数
- is_early_leave: 是否早退（0=否，1=是）
- early_leave_minutes: 早退分钟数
- status: 考勤状态（normal/late/early_leave/overtime/absent）

### 日志表 (logs)
- id: 日志ID（主键）
- operation: 操作类型
- user: 操作用户
- timestamp: 时间戳
- details: 详细信息

## 安装和编译

### 1. 安装依赖
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install gcc sqlite3 libsqlite3-dev

# 或使用Makefile
make install-deps
```

### 2. 编译程序
```bash
# 方法1: 使用Makefile (如果系统支持make)
make all

# 方法2: 使用编译脚本 (推荐)
chmod +x build.sh
./build.sh

# 方法3: 手动编译
gcc -Wall -Wextra -std=c99 -pthread -Wno-format-truncation -o server server.c -lsqlite3 -lpthread
gcc -Wall -Wextra -std=c99 -pthread -Wno-format-truncation -o client client.c
gcc -Wall -Wextra -std=c99 -pthread -Wno-format-truncation -o admin admin.c
```

## 数据库迁移

如果您之前使用的是 `users.db`，现在需要迁移到 `company.db`：

```bash
# 使用迁移脚本（推荐）
chmod +x migrate_database.sh
./migrate_database.sh

# 或手动重命名
mv users.db company.db
```

## 使用说明

### 1. 启动服务器
```bash
./server
```
服务器将在端口8080上监听客户端连接，并自动创建数据库表结构。

### 2. 员工客户端使用
```bash
./client
```

**功能菜单：**
- 登录验证
- 上班打卡（自动检测迟到）
- 下班打卡（自动检测早退和加班）
- 查询个人考勤记录（显示迟到/早退信息）
- 查询考勤统计（含迟到/早退统计）
- 查看工作时间规定

**默认测试账户：**
- 普通员工: 需要管理员添加

### 3. 管理员客户端使用
```bash
./admin
```

**默认管理员账户：**
- 用户名: admin
- 密码: admin123

**管理功能：**
- 添加用户
- 修改用户
- 删除用户
- 查询用户列表
- 查询特定用户考勤记录
- 查询所有用户考勤记录

## 网络协议

系统使用TCP协议，命令格式为逗号分隔的字符串：

### 客户端命令
- `login,username,password` - 用户登录
- `checkin,username` - 上班打卡
- `checkout,username` - 下班打卡
- `get_attendance,username` - 查询个人考勤记录
- `get_overtime,username` - 查询加班统计

### 管理员命令
- `admin_add_user,admin_user,new_username,password,role` - 添加用户
- `admin_delete_user,admin_user,target_username` - 删除用户
- `admin_get_users,admin_user` - 查询用户列表

## 业务逻辑

### 考勤规则
- **标准工作时间**: 09:00-18:00 (8小时)
- **迟到判定**: 09:00后上班打卡视为迟到
- **早退判定**: 18:00前下班打卡视为早退
- **加班判定**: 工作时长超过8小时视为加班
- **打卡限制**: 每天只能上班打卡一次，下班打卡一次
- **打卡顺序**: 必须先上班打卡才能下班打卡
- **时长计算**: 工作时长 = 下班时间 - 上班时间

### 迟到早退检测功能

#### 检测规则
- **迟到检测**:
  - 标准上班时间: 09:00
  - 09:01及以后打卡均视为迟到
  - 自动计算迟到分钟数
  - 打卡时显示迟到提醒

- **早退检测**:
  - 标准下班时间: 18:00
  - 17:59及以前下班打卡均视为早退
  - 自动计算早退分钟数
  - 打卡时显示早退提醒

- **加班检测**:
  - 18:01及以后下班打卡可能产生加班
  - 加班时长 = 实际工作时长 - 8小时
  - 自动计算加班时长

#### 状态分类
- `normal`: 正常考勤
- `late`: 迟到
- `early_leave`: 早退
- `overtime`: 加班
- `absent`: 缺勤

#### 统计功能
- 月度迟到天数和总时长
- 月度早退天数和总时长
- 月度加班天数和总时长
- 出勤率计算

### 权限控制
- 普通员工只能操作自己的考勤记录
- 管理员可以管理所有用户和查看所有考勤记录
- 所有操作都会记录日志

## 开发说明

### 编译选项
- `-pthread`: 多线程支持
- `-lsqlite3`: SQLite3库链接
- `-Wall -Wextra`: 启用警告

### 多线程设计
- 主线程负责接受客户端连接
- 每个客户端连接创建独立的处理线程
- 使用线程分离模式自动清理资源

### 错误处理
- 数据库操作异常处理
- 网络连接异常处理
- 输入参数验证

## 故障排除

### 常见问题
1. **编译错误**: 确保安装了gcc和sqlite3开发库
2. **格式截断警告**: 使用 `-Wno-format-truncation` 编译选项或使用提供的build.sh脚本
3. **连接失败**: 检查服务器是否启动，端口是否被占用
4. **数据库错误**: 确保有写入权限，数据库文件未损坏

### 调试模式
服务器会输出详细的连接和操作日志，便于调试。

## 扩展功能

系统设计支持以下扩展：
- 添加更多考勤规则（迟到、早退检测）
- 支持部门管理
- 添加Web界面
- 数据导出功能
- 邮件通知功能

## 许可证

本项目仅供学习和研究使用。
