#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <time.h>

#pragma comment(lib, "ws2_32.lib")

#define PORT 8080
#define SERVER_IP_ADDR "127.0.0.1"
#define BUFFER_SIZE 1024

// 显示管理员菜单
void show_admin_menu() {
    printf("\n===== 管理员控制台 =====\n");
    printf("1. 添加用户\n");
    printf("2. 修改用户\n");
    printf("3. 删除用户\n");
    printf("4. 查询用户列表\n");
    printf("5. 查询特定用户考勤记录\n");
    printf("6. 查询所有用户考勤记录\n");
    printf("0. 退出系统\n");
    printf("请选择操作: ");
}

int main(int argc, char *argv[]) {
    // 初始化Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        printf("WSAStartup failed: %d\n", WSAGetLastError());
        return 1;
    }

    // 创建套接字
    int admin_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (admin_fd == INVALID_SOCKET) {
        perror("创建套接字失败");
        WSACleanup();
        exit(EXIT_FAILURE);
    }

    // 定义服务器地址结构
    struct sockaddr_in server_addr = {0};
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(PORT);
    inet_pton(AF_INET, SERVER_IP_ADDR, &(server_addr.sin_addr));

    // 连接服务器
    if (connect(admin_fd, (struct sockaddr *)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        perror("连接服务器失败");
        closesocket(admin_fd);
        WSACleanup();
        exit(EXIT_FAILURE);
    }

    puts("连接服务器成功");

    char buffer[BUFFER_SIZE];
    char username[50];
    bool logged_in = false;
    int user_role = -1;

    // 管理员登录流程
    while (!logged_in) {
        printf("请输入管理员用户名: ");
        fgets(username, sizeof(username), stdin);
        username[strcspn(username, "\n")] = '\0';

        if (strcmp(username, "exit") == 0) {
            closesocket(admin_fd);
            WSACleanup();
            return 0;
        }

        char password[50];
        printf("请输入密码: ");
        fgets(password, sizeof(password), stdin);
        password[strcspn(password, "\n")] = '\0';

        // 发送登录命令
        snprintf(buffer, BUFFER_SIZE, "login,%s,%s", username, password);
        ssize_t count = send(admin_fd, buffer, strlen(buffer), 0);
        if (count == SOCKET_ERROR) {
            perror("发送登录信息失败");
            continue;
        }

        // 接收登录结果
        count = recv(admin_fd, buffer, BUFFER_SIZE - 1, 0);
        if (count > 0) {
            buffer[count] = '\0';
            printf("登录结果: %s\n", buffer);

            // 解析登录结果
            if (strstr(buffer, "登录成功") != NULL) {
                // 提取用户角色
                char *role_str = strchr(buffer, ',');
                if (role_str != NULL) {
                    user_role = atoi(role_str + 1);
                    if (user_role == 0) {
                        logged_in = true;
                        printf("管理员登录成功\n");
                    } else {
                        printf("非管理员账户，无法登录管理控制台\n");
                    }
                }
            }
        } else {
            printf("无响应\n");
        }
    }

    // 管理员菜单循环
    while (true) {
        show_admin_menu();
        int choice;
        scanf("%d", &choice);
        // 清除输入缓冲区
        while (getchar() != '\n');

        switch (choice) {
            case 1: {
                // 添加用户
                char new_username[50], new_password[50];
                int new_role;

                printf("请输入新用户名: ");
                fgets(new_username, sizeof(new_username), stdin);
                new_username[strcspn(new_username, "\n")] = '\0';

                printf("请输入新密码: ");
                fgets(new_password, sizeof(new_password), stdin);
                new_password[strcspn(new_password, "\n")] = '\0';

                printf("请输入用户角色 (0:管理员, 1:普通员工): ");
                scanf("%d", &new_role);
                while (getchar() != '\n');

                // 发送添加用户命令
                snprintf(buffer, BUFFER_SIZE, "admin_add_user,%s,%s,%s,%d", username, new_username, new_password, new_role);
                ssize_t count = send(admin_fd, buffer, strlen(buffer), 0);
                if (count == SOCKET_ERROR) {
                    perror("发送添加用户信息失败");
                    break;
                }

                // 接收添加用户结果
                count = recv(admin_fd, buffer, BUFFER_SIZE - 1, 0);
                if (count > 0) {
                    buffer[count] = '\0';
                    printf("添加用户结果: %s\n", buffer);
                } else {
                    printf("无响应\n");
                }
                break;
            }
            case 2: {
                // 修改用户
                char target_username[50], new_password[50];
                int new_role;

                printf("请输入要修改的用户名: ");
                fgets(target_username, sizeof(target_username), stdin);
                target_username[strcspn(target_username, "\n")] = '\0';

                printf("请输入新密码 (直接回车不修改): ");
                fgets(new_password, sizeof(new_password), stdin);
                new_password[strcspn(new_password, "\n")] = '\0';

                printf("请输入新角色 (0:管理员, 1:普通员工, -1不修改): ");
                scanf("%d", &new_role);
                while (getchar() != '\n');

                // 发送修改用户命令
                snprintf(buffer, BUFFER_SIZE, "admin_update_user,%s,%s,%s,%d", username, target_username, new_password, new_role);
                ssize_t count = send(admin_fd, buffer, strlen(buffer), 0);
                if (count == SOCKET_ERROR) {
                    perror("发送修改用户信息失败");
                    break;
                }

                // 接收修改用户结果
                count = recv(admin_fd, buffer, BUFFER_SIZE - 1, 0);
                if (count > 0) {
                    buffer[count] = '\0';
                    printf("修改用户结果: %s\n", buffer);
                } else {
                    printf("无响应\n");
                }
                break;
            }
            case 3: {
                // 删除用户
                char target_username[50];

                printf("请输入要删除的用户名: ");
                fgets(target_username, sizeof(target_username), stdin);
                target_username[strcspn(target_username, "\n")] = '\0';

                // 确认删除
                printf("确定要删除用户 %s 吗? (y/n): ");
                char confirm;
                scanf("%c", &confirm);
                while (getchar() != '\n');

                if (confirm != 'y' && confirm != 'Y') {
                    printf("取消删除\n");
                    break;
                }

                // 发送删除用户命令
                snprintf(buffer, BUFFER_SIZE, "admin_delete_user,%s,%s", username, target_username);
                ssize_t count = send(admin_fd, buffer, strlen(buffer), 0);
                if (count == SOCKET_ERROR) {
                    perror("发送删除用户信息失败");
                    break;
                }

                // 接收删除用户结果
                count = recv(admin_fd, buffer, BUFFER_SIZE - 1, 0);
                if (count > 0) {
                    buffer[count] = '\0';
                    printf("删除用户结果: %s\n", buffer);
                } else {
                    printf("无响应\n");
                }
                break;
            }
            case 4: {
                // 查询用户列表
                snprintf(buffer, BUFFER_SIZE, "admin_get_users,%s", username);
                ssize_t count = send(admin_fd, buffer, strlen(buffer), 0);
                if (count == SOCKET_ERROR) {
                    perror("发送查询用户列表信息失败");
                    break;
                }

                // 接收用户列表
                count = recv(admin_fd, buffer, BUFFER_SIZE - 1, 0);
                if (count > 0) {
                    buffer[count] = '\0';
                    printf("用户列表:\n%s\n", buffer);
                } else {
                    printf("无响应\n");
                }
                break;
            }
            case 5: {
                // 查询特定用户考勤记录
                char target_username[50];

                printf("请输入要查询的用户名: ");
                fgets(target_username, sizeof(target_username), stdin);
                target_username[strcspn(target_username, "\n")] = '\0';

                snprintf(buffer, BUFFER_SIZE, "admin_get_user_attendance,%s,%s", username, target_username);
                ssize_t count = send(admin_fd, buffer, strlen(buffer), 0);
                if (count == SOCKET_ERROR) {
                    perror("发送查询考勤记录信息失败");
                    break;
                }

                // 接收考勤记录
                count = recv(admin_fd, buffer, BUFFER_SIZE - 1, 0);
                if (count > 0) {
                    buffer[count] = '\0';
                    printf("%s的考勤记录:\n%s\n", target_username, buffer);
                } else {
                    printf("无响应\n");
                }
                break;
            }
            case 6: {
                // 查询所有用户考勤记录
                snprintf(buffer, BUFFER_SIZE, "admin_get_all_attendance,%s", username);
                ssize_t count = send(admin_fd, buffer, strlen(buffer), 0);
                if (count == SOCKET_ERROR) {
                    perror("发送查询所有考勤记录信息失败");
                    break;
                }

                // 接收所有考勤记录
                count = recv(admin_fd, buffer, BUFFER_SIZE - 1, 0);
                if (count > 0) {
                    buffer[count] = '\0';
                    printf("所有用户考勤记录:\n%s\n", buffer);
                } else {
                    printf("无响应\n");
                }
                break;
            }
            case 0: {
                // 退出系统
                printf("退出系统\n");
                closesocket(admin_fd);
                WSACleanup();
                return 0;
            }
            default:
                printf("无效的选择，请重新输入\n");
                break;
        }
    }

    closesocket(admin_fd);
    WSACleanup();
    return 0;
}