#!/bin/bash

# 简化的编译脚本
# 解决格式截断警告问题

echo "编译员工考勤系统..."

# 编译选项
CFLAGS="-Wall -Wextra -std=c99 -pthread"
LIBS="-lsqlite3 -lpthread"

# 如果gcc版本支持，添加禁用格式截断警告的选项
GCC_VERSION=$(gcc -dumpversion | cut -d. -f1)
if [ "$GCC_VERSION" -ge 7 ]; then
    CFLAGS="$CFLAGS -Wno-format-truncation"
fi

echo "使用编译选项: $CFLAGS"

# 编译服务器
echo "编译 server..."
gcc $CFLAGS -o server server.c $LIBS
if [ $? -ne 0 ]; then
    echo "server 编译失败"
    exit 1
fi

# 编译客户端
echo "编译 client..."
gcc $CFLAGS -o client client.c
if [ $? -ne 0 ]; then
    echo "client 编译失败"
    exit 1
fi

# 编译管理端
echo "编译 admin..."
gcc $CFLAGS -o admin admin.c
if [ $? -ne 0 ]; then
    echo "admin 编译失败"
    exit 1
fi

echo "编译完成！"
echo ""
echo "生成的可执行文件："
ls -la server client admin

echo ""
echo "使用方法："
echo "1. 启动服务器: ./server"
echo "2. 启动员工客户端: ./client"
echo "3. 启动管理员客户端: ./admin"
