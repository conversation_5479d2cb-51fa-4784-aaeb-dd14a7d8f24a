/***************************************************
# File Name:    server.c
# Author:       wang
# Mail:         <EMAIL>
# Created Time: 2025年08月14日 星期四 17时45分27秒
****************************************************/

#include <stdio.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <netinet/in.h> 
#include <arpa/inet.h> 
#include <sys/socket.h>
#include <sqlite3.h>

#define PORT 8080

int main(int argc, char *argv[]) {
    //创建套接字
    int server_fd = socket(AF_INET,SOCK_STREAM,0);
    if(server_fd == -1){
        perror("创建套接字失败");
        exit(EXIT_FAILURE);
    }
    //绑定端口号
    struct sockaddr_in server_addr = {0};
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(PORT);
    server_addr.sin_addr.s_addr = inet_addr("127.0.0.1");
    if(bind(server_fd,(struct sockaddr *)&server_addr,sizeof(server_addr)) == -1){
        perror("绑定端口失败");
        close(server_fd);
        exit(EXIT_FAILURE);
    }
    //监听端口号
    if(listen(server_fd,5) == -1){
        perror("监听端口失败");
        close(server_fd);
        exit(EXIT_FAILURE);
    }
    printf("服务端正常监听%d端口\n",PORT);
    //等待接收
    //定义客户端sockaddr信息
    struct sockaddr_in client_addr = {0};
    int client_addr_len = sizeof(client_addr);
    int client_fd = accept(server_fd,(struct sockaddr *)&client_addr,&client_addr_len);
    if(client_fd == -1){
        perror("客户端连接出错\n");
        close(server_fd);
        exit(EXIT_FAILURE);
      }
        printf("有客户端来连接服务端\n");
    //读写
    char str[128];
    char response[256];
    //read--低级IO，参数：客户端文件描述符，存放数据的字符数组，每次最多读取多少字符
    ssize_t count = 0;
    //循环读取
    while(true){
        //读取客户端数据
        if((count = read(client_fd,str,sizeof(str)- 1)) > 0){
            //如果大于0证明没用读完
            //添加结束符
            str[count] = '\0';
            printf("服务端接收客户端的登录信息: %s\n",str);

            // 解析用户名和密码
            char* username = strtok(str, ",");
            char* password = strtok(NULL, ",");

            if(username == NULL || password == NULL){
                snprintf(response, sizeof(response), "登录失败: 无效的登录格式");
            } else {
                // 验证用户名和密码
                bool login_success = false;
                sqlite3 *db;
                int rc = sqlite3_open("users.db", &db);
                if(rc != SQLITE_OK){
                    fprintf(stderr, "无法打开数据库: %s\n", sqlite3_errmsg(db));
                    snprintf(response, sizeof(response), "登录失败: 服务器内部错误");
                } else {
                    const char *sql = "SELECT password FROM users WHERE username = ?";
                    sqlite3_stmt *stmt;
                    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
                    if(rc != SQLITE_OK){
                        fprintf(stderr, "准备语句失败: %s\n", sqlite3_errmsg(db));
                        snprintf(response, sizeof(response), "登录失败: 服务器内部错误");
                    } else {
                        // 绑定参数
                        sqlite3_bind_text(stmt, 1, username, -1, SQLITE_STATIC);

                        // 执行查询
                        rc = sqlite3_step(stmt);
                        if(rc == SQLITE_ROW){
                            // 找到了用户
                            const char *stored_password = (const char *)sqlite3_column_text(stmt, 0);
                            if(strcmp(password, stored_password) == 0){
                                login_success = true;
                            }
                        }

                        // 清理
                        sqlite3_finalize(stmt);
                    }

                    sqlite3_close(db);

                    if(login_success){
                        snprintf(response, sizeof(response), "登录成功");
                    } else {
                        snprintf(response, sizeof(response), "登录失败: 用户名或密码错误");
                    }
                }
            }

            //向客户端传输数据
            count = write(client_fd, response, strlen(response));
            if(count > 0){
                printf("返回登录结果: %s\n", response);
            } else if(count == -1){
                perror("发送响应失败\n");
                break;
            }
        }else if(count == 0){
            //当前客户端提前下线
            printf("客户端已断开连接\n");
            break;
        }else {
            //小于0
            perror("读取数据异常\n");
            break;
        }
    }
    close(server_fd);
    close(client_fd);
    return 0;
}

