/***************************************************
# File Name:    server.c
# Author:       wang
# Mail:         <EMAIL>
# Created Time: 2025年08月14日 星期四 17时45分27秒
# Modified:     2025年08月16日 - 添加多线程和完整考勤系统功能
****************************************************/

#include <stdio.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <sqlite3.h>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #include <windows.h>
    #pragma comment(lib, "ws2_32.lib")
    #define close closesocket
    typedef HANDLE pthread_t;
    typedef void* pthread_attr_t;
#else
    #include <unistd.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <sys/socket.h>
    #include <pthread.h>
#endif

#define PORT 8080
#define BUFFER_SIZE 1024
#define MAX_CLIENTS 100

// 客户端信息结构
typedef struct {
    int socket_fd;
    struct sockaddr_in address;
} client_info_t;

// 数据库初始化函数
int init_database() {
    sqlite3 *db;
    char *err_msg = 0;
    int rc = sqlite3_open("users.db", &db);

    if (rc != SQLITE_OK) {
        fprintf(stderr, "无法打开数据库: %s\n", sqlite3_errmsg(db));
        return -1;
    }

    // 创建用户表
    const char *create_users_table =
        "CREATE TABLE IF NOT EXISTS users ("
        "id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "name TEXT UNIQUE NOT NULL,"
        "password TEXT NOT NULL,"
        "role INTEGER NOT NULL DEFAULT 1"  // 0:管理员, 1:普通员工
        ");";

    // 创建考勤记录表
    const char *create_attendance_table =
        "CREATE TABLE IF NOT EXISTS attendance ("
        "id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "user_id INTEGER NOT NULL,"
        "date TEXT NOT NULL,"
        "check_in_time TEXT,"
        "check_out_time TEXT,"
        "work_hours REAL DEFAULT 0,"
        "overtime_hours REAL DEFAULT 0,"
        "FOREIGN KEY(user_id) REFERENCES users(id)"
        ");";

    // 创建日志表
    const char *create_logs_table =
        "CREATE TABLE IF NOT EXISTS logs ("
        "id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "operation TEXT NOT NULL,"
        "user TEXT NOT NULL,"
        "timestamp TEXT NOT NULL,"
        "details TEXT"
        ");";

    // 执行创建表语句
    rc = sqlite3_exec(db, create_users_table, 0, 0, &err_msg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "创建用户表失败: %s\n", err_msg);
        sqlite3_free(err_msg);
        sqlite3_close(db);
        return -1;
    }

    rc = sqlite3_exec(db, create_attendance_table, 0, 0, &err_msg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "创建考勤表失败: %s\n", err_msg);
        sqlite3_free(err_msg);
        sqlite3_close(db);
        return -1;
    }

    rc = sqlite3_exec(db, create_logs_table, 0, 0, &err_msg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "创建日志表失败: %s\n", err_msg);
        sqlite3_free(err_msg);
        sqlite3_close(db);
        return -1;
    }

    // 插入默认管理员账户（如果不存在）
    const char *insert_admin =
        "INSERT OR IGNORE INTO users (name, password, role) VALUES ('admin', 'admin123', 0);";
    rc = sqlite3_exec(db, insert_admin, 0, 0, &err_msg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "插入默认管理员失败: %s\n", err_msg);
        sqlite3_free(err_msg);
    }

    sqlite3_close(db);
    printf("数据库初始化完成\n");
    return 0;
}

// 记录日志函数
void log_operation(const char *operation, const char *user, const char *details) {
    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "无法打开数据库记录日志: %s\n", sqlite3_errmsg(db));
        return;
    }

    time_t now;
    time(&now);
    char *timestamp = ctime(&now);
    timestamp[strlen(timestamp) - 1] = '\0'; // 移除换行符

    const char *sql = "INSERT INTO logs (operation, user, timestamp, details) VALUES (?, ?, ?, ?)";
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc == SQLITE_OK) {
        sqlite3_bind_text(stmt, 1, operation, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 2, user, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 3, timestamp, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 4, details, -1, SQLITE_STATIC);
        sqlite3_step(stmt);
        sqlite3_finalize(stmt);
    }

    sqlite3_close(db);
}

int main(int argc, char *argv[]) {
    //创建套接字
    int server_fd = socket(AF_INET,SOCK_STREAM,0);
    if(server_fd == -1){
        perror("创建套接字失败");
        exit(EXIT_FAILURE);
    }
    //绑定端口号
    struct sockaddr_in server_addr = {0};
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(PORT);
    server_addr.sin_addr.s_addr = inet_addr("127.0.0.1");
    if(bind(server_fd,(struct sockaddr *)&server_addr,sizeof(server_addr)) == -1){
        perror("绑定端口失败");
        close(server_fd);
        exit(EXIT_FAILURE);
    }
    //监听端口号
    if(listen(server_fd,5) == -1){
        perror("监听端口失败");
        close(server_fd);
        exit(EXIT_FAILURE);
    }
    printf("服务端正常监听%d端口\n",PORT);
    //等待接收
    //定义客户端sockaddr信息
    struct sockaddr_in client_addr = {0};
    int client_addr_len = sizeof(client_addr);
    int client_fd = accept(server_fd,(struct sockaddr *)&client_addr,&client_addr_len);
    if(client_fd == -1){
        perror("客户端连接出错\n");
        close(server_fd);
        exit(EXIT_FAILURE);
      }
        printf("有客户端来连接服务端\n");
    //读写
    char str[128];
    char response[256];
    //read--低级IO，参数：客户端文件描述符，存放数据的字符数组，每次最多读取多少字符
    ssize_t count = 0;
    //循环读取
    while(true){
        //读取客户端数据
        if((count = read(client_fd,str,sizeof(str)- 1)) > 0){
            //如果大于0证明没用读完
            //添加结束符
            str[count] = '\0';
            printf("服务端接收客户端的登录信息: %s\n",str);

            // 解析用户名和密码
            char* username = strtok(str, ",");
            char* password = strtok(NULL, ",");

            if(username == NULL || password == NULL){
                snprintf(response, sizeof(response), "登录失败: 无效的登录格式");
            } else {
                // 验证用户名和密码
                bool login_success = false;
                sqlite3 *db;
                int rc = sqlite3_open("users.db", &db);
                if(rc != SQLITE_OK){
                    fprintf(stderr, "无法打开数据库: %s\n", sqlite3_errmsg(db));
                    snprintf(response, sizeof(response), "登录失败: 服务器内部错误");
                } else {
                    const char *sql = "SELECT password FROM users WHERE username = ?";
                    sqlite3_stmt *stmt;
                    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
                    if(rc != SQLITE_OK){
                        fprintf(stderr, "准备语句失败: %s\n", sqlite3_errmsg(db));
                        snprintf(response, sizeof(response), "登录失败: 服务器内部错误");
                    } else {
                        // 绑定参数
                        sqlite3_bind_text(stmt, 1, username, -1, SQLITE_STATIC);

                        // 执行查询
                        rc = sqlite3_step(stmt);
                        if(rc == SQLITE_ROW){
                            // 找到了用户
                            const char *stored_password = (const char *)sqlite3_column_text(stmt, 0);
                            if(strcmp(password, stored_password) == 0){
                                login_success = true;
                            }
                        }

                        // 清理
                        sqlite3_finalize(stmt);
                    }

                    sqlite3_close(db);

                    if(login_success){
                        snprintf(response, sizeof(response), "登录成功");
                    } else {
                        snprintf(response, sizeof(response), "登录失败: 用户名或密码错误");
                    }
                }
            }

            //向客户端传输数据
            count = write(client_fd, response, strlen(response));
            if(count > 0){
                printf("返回登录结果: %s\n", response);
            } else if(count == -1){
                perror("发送响应失败\n");
                break;
            }
        }else if(count == 0){
            //当前客户端提前下线
            printf("客户端已断开连接\n");
            break;
        }else {
            //小于0
            perror("读取数据异常\n");
            break;
        }
    }
    close(server_fd);
    close(client_fd);
    return 0;
}

