#!/bin/bash

# 编译测试脚本
# 用于测试编译是否成功，是否有警告

echo "===== 员工考勤系统编译测试 ====="
echo ""

# 清理之前的编译文件
echo "1. 清理之前的编译文件..."
rm -f server client admin

echo "2. 编译 server.c..."
gcc -Wall -Wextra -std=c99 -pthread -Wno-format-truncation -o server server.c -lsqlite3 -lpthread
if [ $? -eq 0 ]; then
    echo "   ✓ server.c 编译成功"
else
    echo "   ✗ server.c 编译失败"
    exit 1
fi

echo "3. 编译 client.c..."
gcc -Wall -Wextra -std=c99 -pthread -Wno-format-truncation -o client client.c
if [ $? -eq 0 ]; then
    echo "   ✓ client.c 编译成功"
else
    echo "   ✗ client.c 编译失败"
    exit 1
fi

echo "4. 编译 admin.c..."
gcc -Wall -Wextra -std=c99 -pthread -Wno-format-truncation -o admin admin.c
if [ $? -eq 0 ]; then
    echo "   ✓ admin.c 编译成功"
else
    echo "   ✗ admin.c 编译失败"
    exit 1
fi

echo ""
echo "===== 编译测试完成 ====="
echo "所有程序编译成功！"

echo ""
echo "生成的文件："
ls -la server client admin 2>/dev/null

echo ""
echo "可以运行以下命令启动系统："
echo "  ./server     # 启动服务器"
echo "  ./client     # 启动员工客户端"
echo "  ./admin      # 启动管理员客户端"

echo ""
echo "注意事项："
echo "1. 确保已安装 sqlite3 开发库"
echo "2. 服务器默认监听 8080 端口"
echo "3. 默认管理员账户: admin/admin123"
