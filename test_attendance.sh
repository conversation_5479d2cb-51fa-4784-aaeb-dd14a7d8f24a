#!/bin/bash

# 员工考勤系统测试脚本
# 用于测试迟到和早退功能

echo "===== 员工考勤系统测试 ====="
echo "测试迟到和早退检测功能"
echo ""

# 检查是否已编译
if [ ! -f "./server" ] || [ ! -f "./client" ] || [ ! -f "./admin" ]; then
    echo "正在编译程序..."
    make all
    if [ $? -ne 0 ]; then
        echo "编译失败，请检查依赖是否安装"
        echo "运行: make install-deps"
        exit 1
    fi
fi

echo "1. 启动服务器（后台运行）"
./server &
SERVER_PID=$!
sleep 2

echo "2. 创建测试用户"
# 这里需要手动通过admin客户端添加测试用户
echo "请使用admin客户端添加测试用户："
echo "用户名: admin, 密码: admin123 (默认管理员)"
echo "可以添加测试员工: test_user, 密码: 123456"

echo ""
echo "3. 测试场景说明："
echo "   - 09:00前打卡: 正常上班"
echo "   - 09:01后打卡: 迟到"
echo "   - 18:00前下班: 早退"
echo "   - 18:00后下班: 正常/加班"

echo ""
echo "4. 手动测试步骤："
echo "   a) 运行 ./admin 添加测试用户"
echo "   b) 运行 ./client 进行考勤测试"
echo "   c) 修改系统时间测试不同时间段的打卡"

echo ""
echo "5. 数据库查询测试："
echo "可以直接查询数据库验证结果："
echo "sqlite3 users.db \"SELECT * FROM attendance;\""

echo ""
echo "服务器已启动 (PID: $SERVER_PID)"
echo "按 Ctrl+C 停止服务器"

# 等待用户中断
trap "echo '正在停止服务器...'; kill $SERVER_PID; exit 0" INT
wait $SERVER_PID
