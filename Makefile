# 员工考勤系统 Makefile
# 编译器设置
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -pthread -Wno-format-truncation
LIBS = -lsqlite3 -lpthread

# 目标文件
TARGETS = server client admin

# 默认目标
all: $(TARGETS)

# 编译服务器
server: server.c
	$(CC) $(CFLAGS) -o server server.c $(LIBS)

# 编译客户端
client: client.c
	$(CC) $(CFLAGS) -o client client.c

# 编译管理端
admin: admin.c
	$(CC) $(CFLAGS) -o admin admin.c

# 清理编译文件
clean:
	rm -f $(TARGETS)

# 运行服务器
run-server: server
	./server

# 运行客户端
run-client: client
	./client

# 运行管理端
run-admin: admin
	./admin

# 初始化数据库（运行一次服务器来创建表）
init-db: server
	timeout 2s ./server || true

# 数据库迁移（从users.db到company.db）
migrate-db:
	chmod +x migrate_database.sh
	./migrate_database.sh

# 安装依赖（Ubuntu/Debian）
install-deps:
	sudo apt-get update
	sudo apt-get install -y gcc sqlite3 libsqlite3-dev

# 帮助信息
help:
	@echo "员工考勤系统编译帮助："
	@echo "make all        - 编译所有程序"
	@echo "make server     - 编译服务器"
	@echo "make client     - 编译客户端"
	@echo "make admin      - 编译管理端"
	@echo "make clean      - 清理编译文件"
	@echo "make run-server - 运行服务器"
	@echo "make run-client - 运行客户端"
	@echo "make run-admin  - 运行管理端"
	@echo "make init-db    - 初始化数据库"
	@echo "make migrate-db - 数据库迁移"
	@echo "make install-deps - 安装依赖包"

.PHONY: all clean run-server run-client run-admin init-db migrate-db install-deps help
