#!/bin/bash

# 数据库迁移脚本
# 将 users.db 迁移到 company.db

echo "===== 数据库迁移脚本 ====="
echo "将 users.db 迁移到 company.db"
echo ""

# 检查是否存在旧的数据库文件
if [ -f "users.db" ]; then
    echo "发现旧数据库文件: users.db"
    
    # 检查是否已存在新数据库文件
    if [ -f "company.db" ]; then
        echo "警告: company.db 已存在"
        echo "请选择操作:"
        echo "1. 备份现有 company.db 并迁移 users.db"
        echo "2. 取消迁移"
        read -p "请输入选择 (1/2): " choice
        
        case $choice in
            1)
                echo "备份现有 company.db 为 company.db.backup"
                cp company.db company.db.backup
                echo "删除现有 company.db"
                rm company.db
                ;;
            2)
                echo "取消迁移"
                exit 0
                ;;
            *)
                echo "无效选择，取消迁移"
                exit 1
                ;;
        esac
    fi
    
    echo "重命名 users.db 为 company.db"
    mv users.db company.db
    
    if [ $? -eq 0 ]; then
        echo "✓ 迁移成功！"
        echo "数据库文件已从 users.db 重命名为 company.db"
    else
        echo "✗ 迁移失败！"
        exit 1
    fi
    
elif [ -f "company.db" ]; then
    echo "✓ 数据库文件 company.db 已存在，无需迁移"
    
else
    echo "未发现数据库文件"
    echo "首次运行服务器时会自动创建 company.db"
fi

echo ""
echo "===== 迁移完成 ====="

# 显示数据库信息
if [ -f "company.db" ]; then
    echo ""
    echo "数据库信息:"
    echo "文件名: company.db"
    echo "文件大小: $(ls -lh company.db | awk '{print $5}')"
    echo "修改时间: $(ls -l company.db | awk '{print $6, $7, $8}')"
    
    # 如果安装了sqlite3，显示表信息
    if command -v sqlite3 &> /dev/null; then
        echo ""
        echo "数据库表结构:"
        sqlite3 company.db ".tables"
        echo ""
        echo "用户数量:"
        sqlite3 company.db "SELECT COUNT(*) as user_count FROM users;"
        echo ""
        echo "考勤记录数量:"
        sqlite3 company.db "SELECT COUNT(*) as attendance_count FROM attendance;"
    fi
fi
