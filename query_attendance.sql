-- 员工考勤系统数据查询脚本
-- 使用方法: sqlite3 users.db < query_attendance.sql

.headers on
.mode column

-- 查看所有用户
SELECT '===== 用户列表 =====' as info;
SELECT id, name, 
       CASE role 
           WHEN 0 THEN '管理员' 
           WHEN 1 THEN '普通员工' 
           ELSE '未知' 
       END as role_name
FROM users;

-- 查看最近的考勤记录
SELECT '' as separator;
SELECT '===== 最近考勤记录 =====' as info;
SELECT u.name as 用户名,
       a.date as 日期,
       a.check_in_time as 上班时间,
       a.check_out_time as 下班时间,
       printf('%.1f', a.work_hours) as 工作时长,
       printf('%.1f', a.overtime_hours) as 加班时长,
       CASE 
           WHEN a.is_late = 1 THEN '迟到' || a.late_minutes || '分钟'
           ELSE '正常'
       END as 迟到情况,
       CASE 
           WHEN a.is_early_leave = 1 THEN '早退' || a.early_leave_minutes || '分钟'
           ELSE '正常'
       END as 早退情况,
       a.status as 状态
FROM attendance a
JOIN users u ON a.user_id = u.id
ORDER BY a.date DESC, a.id DESC
LIMIT 10;

-- 查看迟到统计
SELECT '' as separator;
SELECT '===== 迟到统计 =====' as info;
SELECT u.name as 用户名,
       COUNT(*) as 总出勤天数,
       SUM(a.is_late) as 迟到天数,
       SUM(a.late_minutes) as 总迟到分钟,
       printf('%.1f%%', 
           CASE 
               WHEN COUNT(*) > 0 THEN (SUM(a.is_late) * 100.0 / COUNT(*))
               ELSE 0 
           END
       ) as 迟到率
FROM attendance a
JOIN users u ON a.user_id = u.id
WHERE a.check_in_time IS NOT NULL
GROUP BY u.id, u.name
HAVING COUNT(*) > 0;

-- 查看早退统计
SELECT '' as separator;
SELECT '===== 早退统计 =====' as info;
SELECT u.name as 用户名,
       COUNT(*) as 总出勤天数,
       SUM(a.is_early_leave) as 早退天数,
       SUM(a.early_leave_minutes) as 总早退分钟,
       printf('%.1f%%', 
           CASE 
               WHEN COUNT(*) > 0 THEN (SUM(a.is_early_leave) * 100.0 / COUNT(*))
               ELSE 0 
           END
       ) as 早退率
FROM attendance a
JOIN users u ON a.user_id = u.id
WHERE a.check_out_time IS NOT NULL
GROUP BY u.id, u.name
HAVING COUNT(*) > 0;

-- 查看加班统计
SELECT '' as separator;
SELECT '===== 加班统计 =====' as info;
SELECT u.name as 用户名,
       COUNT(*) as 总出勤天数,
       SUM(CASE WHEN a.overtime_hours > 0 THEN 1 ELSE 0 END) as 加班天数,
       printf('%.1f', SUM(a.overtime_hours)) as 总加班小时,
       printf('%.1f', 
           CASE 
               WHEN COUNT(*) > 0 THEN (SUM(a.overtime_hours) / COUNT(*))
               ELSE 0 
           END
       ) as 平均加班小时
FROM attendance a
JOIN users u ON a.user_id = u.id
WHERE a.check_out_time IS NOT NULL
GROUP BY u.id, u.name
HAVING COUNT(*) > 0;

-- 查看本月考勤汇总
SELECT '' as separator;
SELECT '===== 本月考勤汇总 =====' as info;
SELECT u.name as 用户名,
       COUNT(*) as 出勤天数,
       SUM(a.is_late) as 迟到天数,
       SUM(a.is_early_leave) as 早退天数,
       SUM(CASE WHEN a.overtime_hours > 0 THEN 1 ELSE 0 END) as 加班天数,
       printf('%.1f', SUM(a.overtime_hours)) as 总加班小时,
       printf('%.1f%%', 
           CASE 
               WHEN COUNT(*) > 0 THEN ((COUNT(*) - SUM(a.is_late) - SUM(a.is_early_leave)) * 100.0 / COUNT(*))
               ELSE 0 
           END
       ) as 正常出勤率
FROM attendance a
JOIN users u ON a.user_id = u.id
WHERE strftime('%Y-%m', a.date) = strftime('%Y-%m', 'now')
GROUP BY u.id, u.name
HAVING COUNT(*) > 0;

-- 查看操作日志（最近10条）
SELECT '' as separator;
SELECT '===== 最近操作日志 =====' as info;
SELECT operation as 操作类型,
       user as 用户,
       timestamp as 时间,
       details as 详情
FROM logs
ORDER BY id DESC
LIMIT 10;
