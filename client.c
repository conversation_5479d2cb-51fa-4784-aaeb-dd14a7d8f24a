/***************************************************
# File Name:    client.c
# Author:       wang
# Mail:         <EMAIL>
# Created Time: 2025年08月14日 星期四 17时45分35秒
****************************************************/

#include <stdio.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <netinet/in.h> 
#include <arpa/inet.h> 
#include <sys/socket.h>

#define PORT 8080
#define SERVER_IP_ADDR "127.0.0.1"
 
int main(int argc, char *argv[]) {
    //创建套接字
    int client_fd = socket(AF_INET,SOCK_STREAM,0);
    if(client_fd == -1){
        perror("创建套接字失败");
        exit(EXIT_FAILURE);
    }
    //定义客户端struct
    struct sockaddr_in server_addr = {0};
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(PORT);
    server_addr.sin_addr.s_addr = inet_addr(SERVER_IP_ADDR);
    //连接服务端
    int server_fd = connect(client_fd,(struct sockaddr *)&server_addr,sizeof(server_addr));
    if(server_fd == -1){
        perror("连接服务端失败");
        close(server_fd);
        exit(EXIT_FAILURE);
    }
    puts("连接服务端成功");
    char str[128];
    char buf[128];
    ssize_t count = 0;
    //发送数据或者接收数据
    //wrtie
    while(true){
        //客户端向服务端发送数据
        puts("请输入用户名:");
        fgets(str, sizeof(str), stdin);
        // 移除换行符
        str[strcspn(str, "\n")] = '\0';

        if(strcmp(str,"exit") == 0){
            //退出
            break;
        }

        puts("请输入密码:");
        fgets(buf, sizeof(buf), stdin);
        // 移除换行符
        buf[strcspn(buf, "\n")] = '\0';

        // 组合用户名和密码，用逗号分隔
        char login_info[256];
        snprintf(login_info, sizeof(login_info), "%s,%s", str, buf);

        count = 0;
        //参数，客户端文件描述符，发送的数据
        //发送的数据的长度
        count = write(client_fd,login_info,strlen(login_info));
        if(count > 0){
            printf("发送登录信息成功\n");
        }
        //客户端接收服务端数据
        count = 0;
        if((count = read(client_fd,buf,sizeof(buf) - 1)) > 0){
            buf[count] = '\0';
            printf("登录结果: %s\n",buf);
        }else {
            printf("无响应\n");
        }
    }
    close(client_fd);
    return 0;
}
