/***************************************************
# File Name:    client.c
# Author:       wang
# Mail:         <EMAIL>
# Created Time: 2025年08月14日 星期四 17时45分35秒
****************************************************/

#include <stdio.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <netinet/in.h> 
#include <arpa/inet.h> 
#include <sys/socket.h>

#define PORT 8080
#define SERVER_IP_ADDR "127.0.0.1"
 
int main(int argc, char *argv[]) {
    //创建套接字
    int client_fd = socket(AF_INET,SOCK_STREAM,0);
    if(client_fd == -1){
        perror("创建套接字失败");
        exit(EXIT_FAILURE);
    }
    //定义客户端struct
    struct sockaddr_in server_addr = {0};
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(PORT);
    server_addr.sin_addr.s_addr = inet_addr(SERVER_IP_ADDR);
    //连接服务端
    int server_fd = connect(client_fd,(struct sockaddr *)&server_addr,sizeof(server_addr));
    if(server_fd == -1){
        perror("连接服务端失败");
        close(server_fd);
        exit(EXIT_FAILURE);
    }
    puts("连接服务端成功");

    char username[128];
    char password[128];
    char buf[256];
    ssize_t count = 0;
    bool logged_in = false;
    int user_role = -1;

    // 登录流程
    while (!logged_in) {
        printf("请输入用户名 (输入 'exit' 退出): ");
        fgets(username, sizeof(username), stdin);
        username[strcspn(username, "\n")] = '\0';

        if (strcmp(username, "exit") == 0) {
            break;
        }

        printf("请输入密码: ");
        fgets(password, sizeof(password), stdin);
        password[strcspn(password, "\n")] = '\0';

        // 发送登录请求
        char login_info[256];
        snprintf(login_info, sizeof(login_info), "login,%s,%s", username, password);

        count = send(client_fd, login_info, strlen(login_info), 0);
        if (count > 0) {
            printf("发送登录信息成功\n");
        }

        // 接收登录结果
        count = recv(client_fd, buf, sizeof(buf) - 1, 0);
        if (count > 0) {
            buf[count] = '\0';
            printf("登录结果: %s\n", buf);

            // 解析登录结果
            if (strstr(buf, "登录成功") != NULL) {
                char *role_str = strchr(buf, ',');
                if (role_str != NULL) {
                    user_role = atoi(role_str + 1);
                    logged_in = true;
                    printf("登录成功！用户角色: %s\n", user_role == 0 ? "管理员" : "普通员工");
                }
            }
        } else {
            printf("无响应\n");
        }
    }

    // 如果登录成功，显示员工菜单
    if (logged_in && user_role == 1) {  // 普通员工
        while (true) {
            printf("\n===== 员工考勤系统 =====\n");
            printf("用户: %s\n", username);
            printf("1. 上班打卡\n");
            printf("2. 下班打卡\n");
            printf("3. 查询个人考勤记录\n");
            printf("4. 查询考勤统计(含迟到/早退)\n");
            printf("5. 查看工作时间规定\n");
            printf("0. 退出系统\n");
            printf("请选择操作: ");

            int choice;
            scanf("%d", &choice);
            while (getchar() != '\n'); // 清除输入缓冲区

            char command[256];

            switch (choice) {
                case 1: {
                    // 上班打卡
                    snprintf(command, sizeof(command), "checkin,%s", username);
                    count = send(client_fd, command, strlen(command), 0);
                    if (count > 0) {
                        count = recv(client_fd, buf, sizeof(buf) - 1, 0);
                        if (count > 0) {
                            buf[count] = '\0';
                            printf("打卡结果: %s\n", buf);
                        }
                    }
                    break;
                }
                case 2: {
                    // 下班打卡
                    snprintf(command, sizeof(command), "checkout,%s", username);
                    count = send(client_fd, command, strlen(command), 0);
                    if (count > 0) {
                        count = recv(client_fd, buf, sizeof(buf) - 1, 0);
                        if (count > 0) {
                            buf[count] = '\0';
                            printf("打卡结果: %s\n", buf);
                        }
                    }
                    break;
                }
                case 3: {
                    // 查询个人考勤记录
                    snprintf(command, sizeof(command), "get_attendance,%s", username);
                    count = send(client_fd, command, strlen(command), 0);
                    if (count > 0) {
                        count = recv(client_fd, buf, sizeof(buf) - 1, 0);
                        if (count > 0) {
                            buf[count] = '\0';
                            printf("考勤记录:\n%s\n", buf);
                        }
                    }
                    break;
                }
                case 4: {
                    // 查询考勤统计
                    snprintf(command, sizeof(command), "get_overtime,%s", username);
                    count = send(client_fd, command, strlen(command), 0);
                    if (count > 0) {
                        count = recv(client_fd, buf, sizeof(buf) - 1, 0);
                        if (count > 0) {
                            buf[count] = '\0';
                            printf("考勤统计:\n%s\n", buf);
                        }
                    }
                    break;
                }
                case 5: {
                    // 查看工作时间规定
                    printf("\n===== 工作时间规定 =====\n");
                    printf("标准上班时间: 09:00\n");
                    printf("标准下班时间: 18:00\n");
                    printf("标准工作时长: 8小时\n");
                    printf("迟到判定: 超过09:00上班打卡\n");
                    printf("早退判定: 18:00前下班打卡\n");
                    printf("加班判定: 工作时长超过8小时\n");
                    printf("========================\n");
                    break;
                }
                case 0: {
                    printf("退出系统\n");
                    goto exit_program;
                }
                default:
                    printf("无效的选择，请重新输入\n");
                    break;
            }
        }
    } else if (logged_in && user_role == 0) {
        printf("管理员请使用 admin 客户端程序\n");
    }

exit_program:
    close(client_fd);
    return 0;
}
